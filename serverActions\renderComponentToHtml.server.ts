import { renderToStaticMarkup } from "react-dom/server";
import React from "react";

/**
 * Render a React component to HTML string (server-side only)
 * @param Component The React component to render
 * @param props The props to pass to the component
 * @returns HTML string with DOCTYPE
 */
export function renderComponentToHtml<P>(
    Component: React.ComponentType<P>,
    props: P
): string {
    const element = React.createElement(Component as React.ComponentType<any>, props);
    return "<!DOCTYPE html>" + renderToStaticMarkup(element);
}
